<?php
	// Para posts normales, podemos usar términos personalizados o seguir usando metadatos
	$rtp_per = get_post_meta(get_the_id(), 'game_rtp_per', true);
	$volatility = get_post_meta(get_the_id(), 'game_volatility', true);
	$make_new = get_post_meta(get_the_id(), 'game_make_new', true);
?>
<div class="game-box">
    <div class="img-wrap">
        <?php
			if(!empty($make_new)) {
				echo '<span class="new-game">New</span>';
			}
		?>
        <?php the_post_thumbnail(); ?>
    </div>
    <h3 class="title text-center"><?php the_title(); ?></h3>
    <ul>
        <?php if(!empty($rtp_per)): ?>
        <li>
            <strong><?= $rtp_per ?>%</strong>
            <span class="text">RTP<span class="text">
        </li>
        <?php endif; ?>

        <?php if(!empty($volatility)): ?>
        <li class="volatility">
            <div class="bar-wrap">
                <?php
			for ($x = 1; $x <= 5; $x++) {
				if($x <= $volatility) {
					echo "<span class='black bar'></span>";
				} else {
			  		echo "<span class='white bar'></span>";
				}
			} 
			?></div>
            <span class="text">Volatility</span>
        </li>
        <?php endif; ?>
    </ul>
    <div class="btn-wrap">
        <a href="<?php echo get_the_permalink(); ?>" class="button">Play for real <i class="icon-chevron-right"></i></a>
    </div>
</div>