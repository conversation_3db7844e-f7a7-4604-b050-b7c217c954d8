<?php
$args = array(
    'post_type' => 'post',
    'posts_per_page' => 10,
    'tax_query' => array(
        array(
            'taxonomy' => 'post_tag',
            'field'    => 'slug',
            'terms'    => 'game',
        ),
    ),
);

$game_posts = new WP_Query($args);

if ($game_posts->have_posts()) :
    while ($game_posts->have_posts()) : $game_posts->the_post();
        get_template_part('template-parts/content', 'games-loop');
    endwhile;
    wp_reset_postdata();
else :
    echo 'No games found.';
endif;
?>